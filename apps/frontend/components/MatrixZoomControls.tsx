/**
 * 矩阵缩放控制组件
 * 🎯 核心价值：提供直观的矩阵缩放控制界面
 * 📦 功能范围：缩放按钮、适应屏幕、缩放级别显示
 * 🔄 架构设计：独立的控制组件，与矩阵解耦
 */

'use client';

import React from 'react';
import type { MatrixDimensions } from '@/hooks/useResponsiveMatrix';

// ===== 组件属性 =====

interface MatrixZoomControlsProps {
  /** 当前矩阵尺寸信息 */
  dimensions: MatrixDimensions;
  
  /** 手动缩放级别 */
  manualScale: number;
  
  /** 缩放控制函数 */
  onZoomIn: () => void;
  onZoomOut: () => void;
  onResetZoom: () => void;
  onFitToScreen: () => void;
  
  /** 样式配置 */
  className?: string;
  compact?: boolean;
}

// ===== 主组件 =====

const MatrixZoomControls: React.FC<MatrixZoomControlsProps> = ({
  dimensions,
  manualScale,
  onZoomIn,
  onZoomOut,
  onResetZoom,
  onFitToScreen,
  className = '',
  compact = false,
}) => {
  const { cellSize, scale, needsScroll } = dimensions;
  
  // 格式化缩放级别显示
  const formatScale = (scaleValue: number): string => {
    return `${Math.round(scaleValue * 100)}%`;
  };
  
  // 格式化格子尺寸显示
  const formatCellSize = (size: number): string => {
    return `${size}px`;
  };

  if (compact) {
    // 紧凑模式：只显示基本控制
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <button
          onClick={onZoomOut}
          className="p-1 text-sm bg-gray-100 hover:bg-gray-200 rounded transition-colors"
          title="缩小"
          disabled={manualScale <= 0.5}
        >
          🔍-
        </button>
        
        <span className="text-xs text-gray-600 min-w-[40px] text-center">
          {formatScale(scale)}
        </span>
        
        <button
          onClick={onZoomIn}
          className="p-1 text-sm bg-gray-100 hover:bg-gray-200 rounded transition-colors"
          title="放大"
          disabled={manualScale >= 2.0}
        >
          🔍+
        </button>
        
        <button
          onClick={onFitToScreen}
          className="p-1 text-sm bg-blue-100 hover:bg-blue-200 text-blue-800 rounded transition-colors"
          title="适应屏幕"
        >
          📐
        </button>
      </div>
    );
  }

  // 完整模式：显示详细信息和控制
  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-3 ${className}`}>
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-medium text-gray-800">缩放控制</h3>
        <div className="flex items-center space-x-1">
          {needsScroll && (
            <span className="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded">
              滚动
            </span>
          )}
          <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
            {formatCellSize(cellSize)}
          </span>
        </div>
      </div>
      
      {/* 缩放级别显示 */}
      <div className="mb-3">
        <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
          <span>缩放级别</span>
          <span className="font-mono">{formatScale(scale)}</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-500 h-2 rounded-full transition-all duration-200"
            style={{ width: `${Math.min(100, (scale / 2) * 100)}%` }}
          />
        </div>
        <div className="flex justify-between text-xs text-gray-500 mt-1">
          <span>50%</span>
          <span>100%</span>
          <span>200%</span>
        </div>
      </div>
      
      {/* 控制按钮 */}
      <div className="grid grid-cols-2 gap-2">
        <button
          onClick={onZoomOut}
          className="px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          disabled={manualScale <= 0.5}
        >
          🔍- 缩小
        </button>
        
        <button
          onClick={onZoomIn}
          className="px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          disabled={manualScale >= 2.0}
        >
          🔍+ 放大
        </button>
        
        <button
          onClick={onResetZoom}
          className="px-3 py-2 text-sm bg-yellow-100 hover:bg-yellow-200 text-yellow-800 rounded transition-colors"
        >
          🎯 重置
        </button>
        
        <button
          onClick={onFitToScreen}
          className="px-3 py-2 text-sm bg-blue-100 hover:bg-blue-200 text-blue-800 rounded transition-colors"
        >
          📐 适应
        </button>
      </div>
      
      {/* 详细信息 */}
      <div className="mt-3 pt-3 border-t border-gray-200">
        <div className="grid grid-cols-2 gap-2 text-xs text-gray-600">
          <div>
            <span className="block text-gray-500">格子尺寸</span>
            <span className="font-mono">{formatCellSize(cellSize)}</span>
          </div>
          <div>
            <span className="block text-gray-500">矩阵尺寸</span>
            <span className="font-mono">{dimensions.matrixSize}px</span>
          </div>
          <div>
            <span className="block text-gray-500">视口尺寸</span>
            <span className="font-mono">{dimensions.viewportSize}px</span>
          </div>
          <div>
            <span className="block text-gray-500">手动缩放</span>
            <span className="font-mono">{formatScale(manualScale)}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MatrixZoomControls;
