/**
 * 响应式矩阵Hook
 * 🎯 核心价值：动态计算最佳矩阵显示尺寸，确保最佳用户体验
 * 📦 功能范围：窗口尺寸监听、格子尺寸计算、缩放控制、空间优化
 * 🔄 架构设计：响应式设计，自适应不同屏幕尺寸
 */

import { useCallback, useEffect, useState } from 'react';
import { MATRIX_SIZE } from '@/core/matrix/MatrixTypes';

// ===== 类型定义 =====

export interface MatrixDimensions {
  /** 格子尺寸 (px) */
  cellSize: number;
  /** 格子间距 (px) */
  cellGap: number;
  /** 单元总尺寸 (cellSize + cellGap) */
  cellUnit: number;
  /** 矩阵总尺寸 (px) */
  matrixSize: number;
  /** 视口尺寸 (px) */
  viewportSize: number;
  /** 缩放级别 (0.5 - 2.0) */
  scale: number;
  /** 是否需要滚动 */
  needsScroll: boolean;
}

export interface ResponsiveMatrixConfig {
  /** 最小格子尺寸 */
  minCellSize?: number;
  /** 最大格子尺寸 */
  maxCellSize?: number;
  /** 默认格子尺寸 */
  defaultCellSize?: number;
  /** 侧边栏宽度 */
  sidebarWidth?: number;
  /** 工具栏高度 */
  toolbarHeight?: number;
  /** 容器内边距 */
  containerPadding?: number;
  /** 是否显示侧边栏 */
  showSidebar?: boolean;
}

// ===== 默认配置 =====

const DEFAULT_CONFIG: Required<ResponsiveMatrixConfig> = {
  minCellSize: 15,
  maxCellSize: 60,
  defaultCellSize: 30,
  sidebarWidth: 320, // 80 * 4 = 320px (w-80)
  toolbarHeight: 60,
  containerPadding: 32, // p-4 * 2 = 32px
  showSidebar: true,
};

// ===== Hook实现 =====

export function useResponsiveMatrix(config: ResponsiveMatrixConfig = {}) {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  
  // 状态管理
  const [windowSize, setWindowSize] = useState({ width: 0, height: 0 });
  const [manualScale, setManualScale] = useState(1.0);
  const [dimensions, setDimensions] = useState<MatrixDimensions>({
    cellSize: finalConfig.defaultCellSize,
    cellGap: 1,
    cellUnit: finalConfig.defaultCellSize + 1,
    matrixSize: MATRIX_SIZE * (finalConfig.defaultCellSize + 1),
    viewportSize: 400,
    scale: 1.0,
    needsScroll: true,
  });

  // 窗口尺寸监听
  useEffect(() => {
    const updateWindowSize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    // 初始化
    updateWindowSize();

    // 监听窗口变化
    window.addEventListener('resize', updateWindowSize);
    return () => window.removeEventListener('resize', updateWindowSize);
  }, []);

  // 计算可用空间
  const calculateAvailableSpace = useCallback(() => {
    const { width, height } = windowSize;
    
    if (width === 0 || height === 0) {
      return { availableWidth: 400, availableHeight: 400 };
    }

    // 计算可用宽度（减去侧边栏和边距）
    const sidebarWidth = finalConfig.showSidebar ? finalConfig.sidebarWidth : 0;
    const availableWidth = width - sidebarWidth - finalConfig.containerPadding;
    
    // 计算可用高度（减去工具栏和边距）
    const availableHeight = height - finalConfig.toolbarHeight - finalConfig.containerPadding;
    
    return {
      availableWidth: Math.max(200, availableWidth),
      availableHeight: Math.max(200, availableHeight),
    };
  }, [windowSize, finalConfig]);

  // 计算最佳格子尺寸
  const calculateOptimalCellSize = useCallback(() => {
    const { availableWidth, availableHeight } = calculateAvailableSpace();
    
    // 选择较小的尺寸以保持正方形
    const availableSize = Math.min(availableWidth, availableHeight);
    
    // 计算基础格子尺寸（预留1px间距）
    const baseCellSize = Math.floor((availableSize - MATRIX_SIZE) / MATRIX_SIZE);
    
    // 应用手动缩放
    const scaledCellSize = Math.round(baseCellSize * manualScale);
    
    // 限制在最小和最大值之间
    const clampedCellSize = Math.max(
      finalConfig.minCellSize,
      Math.min(finalConfig.maxCellSize, scaledCellSize)
    );
    
    return clampedCellSize;
  }, [calculateAvailableSpace, manualScale, finalConfig]);

  // 更新矩阵尺寸
  useEffect(() => {
    const cellSize = calculateOptimalCellSize();
    const cellGap = 1;
    const cellUnit = cellSize + cellGap;
    const matrixSize = MATRIX_SIZE * cellUnit;
    
    const { availableWidth, availableHeight } = calculateAvailableSpace();
    const viewportSize = Math.min(availableWidth, availableHeight);
    
    const needsScroll = matrixSize > viewportSize;
    const actualScale = cellSize / finalConfig.defaultCellSize;

    setDimensions({
      cellSize,
      cellGap,
      cellUnit,
      matrixSize,
      viewportSize,
      scale: actualScale,
      needsScroll,
    });
  }, [calculateOptimalCellSize, calculateAvailableSpace, finalConfig.defaultCellSize]);

  // 缩放控制函数
  const zoomIn = useCallback(() => {
    setManualScale(prev => Math.min(2.0, prev * 1.2));
  }, []);

  const zoomOut = useCallback(() => {
    setManualScale(prev => Math.max(0.5, prev / 1.2));
  }, []);

  const resetZoom = useCallback(() => {
    setManualScale(1.0);
  }, []);

  const fitToScreen = useCallback(() => {
    // 计算能完全显示矩阵的缩放级别
    const { availableWidth, availableHeight } = calculateAvailableSpace();
    const availableSize = Math.min(availableWidth, availableHeight);
    
    const requiredSize = MATRIX_SIZE * (finalConfig.defaultCellSize + 1);
    const fitScale = availableSize / requiredSize;
    
    setManualScale(Math.max(0.5, Math.min(2.0, fitScale * 0.95))); // 留5%边距
  }, [calculateAvailableSpace, finalConfig.defaultCellSize]);

  // 返回Hook接口
  return {
    // 尺寸信息
    dimensions,
    windowSize,
    
    // 缩放控制
    manualScale,
    zoomIn,
    zoomOut,
    resetZoom,
    fitToScreen,
    
    // 工具函数
    calculateAvailableSpace,
    
    // 配置信息
    config: finalConfig,
  };
}

// ===== 工具函数 =====

/**
 * 格式化尺寸显示
 */
export function formatDimensions(dimensions: MatrixDimensions): string {
  const { cellSize, matrixSize, scale } = dimensions;
  return `${cellSize}px格子 | ${matrixSize}px矩阵 | ${Math.round(scale * 100)}%缩放`;
}

/**
 * 检查是否为移动设备
 */
export function isMobileDevice(): boolean {
  return window.innerWidth < 768;
}

/**
 * 获取推荐的格子尺寸
 */
export function getRecommendedCellSize(screenWidth: number): number {
  if (screenWidth < 768) return 20; // 移动设备
  if (screenWidth < 1024) return 25; // 平板
  if (screenWidth < 1440) return 30; // 小桌面
  return 35; // 大桌面
}
