/**
 * 主应用页面 - 重构版本
 * 🎯 核心价值：数据驱动的矩阵应用，最小化架构，零冗余
 * 📦 功能范围：矩阵渲染、控制面板、模式切换
 * 🔄 架构设计：基于新的核心架构，完全数据驱动的视图
 */

'use client';

import Controls from '@/components/Controls';
import Matrix from '@/components/Matrix';
import MatrixZoomControls from '@/components/MatrixZoomControls';
import React, { useCallback, useEffect, useState } from 'react';

import type { BusinessMode, Coordinate } from '@/core/matrix/MatrixTypes';
import { useResponsiveMatrix } from '@/hooks/useResponsiveMatrix';

// ===== 主应用组件 =====

export default function HomePage() {
  // 本地UI状态（非业务状态）
  const [showControls, setShowControls] = useState(true);
  const [debugMode, setDebugMode] = useState(false);
  const [performanceMonitoring, setPerformanceMonitoring] = useState(false);
  const [isClient, setIsClient] = useState(false);

  // 响应式矩阵控制
  const responsiveMatrix = useResponsiveMatrix({
    showSidebar: showControls,
  });

  // 确保客户端渲染一致性，避免 hydration 错误
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 事件处理
  const handleCellClick = useCallback((coordinate: Coordinate, event: React.MouseEvent) => {
    if (debugMode) {
      console.log('Cell clicked:', coordinate, event);
    }
  }, [debugMode]);

  const handleCellDoubleClick = useCallback((coordinate: Coordinate, event: React.MouseEvent) => {
    if (debugMode) {
      console.log('Cell double-clicked:', coordinate, event);
    }
  }, [debugMode]);

  const handleModeChange = useCallback((mode: BusinessMode) => {
    if (debugMode) {
      console.log('Mode changed to:', mode);
    }
  }, [debugMode]);



  const handleReset = useCallback(() => {
    if (debugMode) {
      console.log('Matrix reset');
    }
  }, [debugMode]);


  // 键盘快捷键
  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (event.ctrlKey || event.metaKey) {
      switch (event.key) {
        case 'h':
          setShowControls(prev => !prev);
          event.preventDefault();
          break;
        case 'd':
          setDebugMode(prev => !prev);
          event.preventDefault();
          break;
        case 'p':
          setPerformanceMonitoring(prev => !prev);
          event.preventDefault();
          break;
        case '=':
        case '+':
          responsiveMatrix.zoomIn();
          event.preventDefault();
          break;
        case '-':
          responsiveMatrix.zoomOut();
          event.preventDefault();
          break;
        case '0':
          responsiveMatrix.resetZoom();
          event.preventDefault();
          break;
        case 'f':
          responsiveMatrix.fitToScreen();
          event.preventDefault();
          break;
      }
    }
  }, [responsiveMatrix]);

  // 在客户端渲染完成前显示加载状态，避免 hydration 错误
  if (!isClient) {
    return (
      <div className="app-container h-screen flex bg-gray-100 items-center justify-center">
        <div className="text-gray-600">加载中...</div>
      </div>
    );
  }

  return (
    <div
      className="app-container h-screen flex bg-gray-100"
      onKeyDown={handleKeyDown}
      tabIndex={-1}
    >
      {/* 控制面板 */}
      {showControls && (
        <div className="controls-sidebar w-80 bg-white border-r border-gray-200 flex-shrink-0">
          <div className="p-4 border-b border-gray-200">
            <h1 className="text-xl font-bold text-gray-800">Cube1 Matrix</h1>
            <p className="text-sm text-gray-600 mt-1">数据驱动的33×33矩阵系统</p>
          </div>

          <div className="space-y-4">
            {/* A组数据控制面板已移除 */}

            {/* 主控制面板 */}
            <Controls
              onModeChange={handleModeChange}
              onReset={handleReset}
            />

            {/* 缩放控制面板 */}
            <div className="p-4">
              <MatrixZoomControls
                dimensions={responsiveMatrix.dimensions}
                manualScale={responsiveMatrix.manualScale}
                onZoomIn={responsiveMatrix.zoomIn}
                onZoomOut={responsiveMatrix.zoomOut}
                onResetZoom={responsiveMatrix.resetZoom}
                onFitToScreen={responsiveMatrix.fitToScreen}
                compact={false}
              />
            </div>
          </div>
        </div>
      )}

      {/* 主矩阵区域 */}
      <div className="matrix-area flex-1 flex flex-col">
        {/* 工具栏 */}
        <div className="toolbar bg-white border-b border-gray-200 px-4 py-2 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setShowControls(prev => !prev)}
              className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded transition-colors"
              title="切换控制面板 (Ctrl+H)"
            >
              {showControls ? '隐藏控制' : '显示控制'}
            </button>

            <button
              onClick={() => setDebugMode(prev => !prev)}
              className={`px-3 py-1 text-sm rounded transition-colors ${debugMode
                ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200'
                : 'bg-gray-100 hover:bg-gray-200'
                }`}
              title="切换调试模式 (Ctrl+D)"
            >
              调试模式
            </button>

            <button
              onClick={() => setPerformanceMonitoring(prev => !prev)}
              className={`px-3 py-1 text-sm rounded transition-colors ${performanceMonitoring
                ? 'bg-green-100 text-green-800 hover:bg-green-200'
                : 'bg-gray-100 hover:bg-gray-200'
                }`}
              title="切换性能监控 (Ctrl+P)"
            >
              性能监控
            </button>
          </div>

          <div className="flex items-center space-x-4">
            {/* 紧凑缩放控制 */}
            <MatrixZoomControls
              dimensions={responsiveMatrix.dimensions}
              manualScale={responsiveMatrix.manualScale}
              onZoomIn={responsiveMatrix.zoomIn}
              onZoomOut={responsiveMatrix.zoomOut}
              onResetZoom={responsiveMatrix.resetZoom}
              onFitToScreen={responsiveMatrix.fitToScreen}
              compact={true}
            />

            <div className="text-sm text-gray-500">
              快捷键: Ctrl**** (模式), Ctrl+H (控制), Ctrl+D (调试), Ctrl+P (性能), Ctrl+/- (缩放), Ctrl+0 (重置), Ctrl+F (适应)
            </div>
          </div>
        </div>

        {/* 矩阵容器 */}
        <div className="matrix-container flex-1 flex items-center justify-center p-4">
          <div className="matrix-wrapper bg-white rounded-lg shadow-sm border border-gray-200">
            <Matrix
              onCellClick={handleCellClick}
              onCellDoubleClick={handleCellDoubleClick}
              onModeChange={handleModeChange}
              dimensions={responsiveMatrix.dimensions}
              showSidebar={showControls}
              enablePerformanceMonitoring={performanceMonitoring}
              debugMode={debugMode}
            />
          </div>
        </div>
      </div>

      {/* 应用专用样式 */}
      <style jsx global>{`
        .app-container:focus {
          outline: none;
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
          .controls-sidebar {
            width: 300px;
          }
        }

        @media (max-width: 768px) {
          .controls-sidebar {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            z-index: 50;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
          }

          .matrix-area {
            width: 100%;
          }
        }
      `}</style>
    </div>
  );
}